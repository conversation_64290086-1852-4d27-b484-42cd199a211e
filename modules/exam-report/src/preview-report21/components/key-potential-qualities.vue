<template>
    <PageHeader />
    <SectionTitle title="关键潜在素质" />
    <div class="potential-qualities__description">
        {{ data.potentialQualitiesKeyDesc }}
    </div>
    <Card class="potential-qualities__card" v-for="item in data.potentialQualities" :key="item.dimensionName">
        <div class="potential-qualities__card-header">
            <SubTitle size="medium" type="title" class="potential-qualities__card-title">{{ item.dimensionName }}</SubTitle>
            <Tag size="medium" status="info" class="potential-qualities__card-tag">{{ item.levelStr }}</Tag>
            <Progress
                class="potential-qualities__card-process"
                :borderRadius="2"
                :footerConfig="false"
                :height="12"
                :minPartWidth="19"
                :partColors="['#F2F2F2']"
                :showTick="true"
                :splitArray="splitArray"
                :totalWidth="'100%'"
                :type="'grow'"
                :value="data.totalScore"
                :valueBarStyle="{ color: getBackgroundColor(item.level) }"
            >
                <template #value-indicator>
                    <div class="number" :style="{ '--bgc': getBackgroundColor(item.level) }">{{ item.score }}</div>
                </template>
            </Progress>
        </div>
        <div class="potential-qualities__card-define">定义：{{ item.dimensionDefine }}</div>
        <div class="potential-qualities__card-performance"><span>表现：</span>{{ getDimensionPerformance(item.dimensionPerformanceList) }}</div>
    </Card>
    <PageFooter :data="pageFooterData" />
</template>

<script setup lang="ts" name="EvaluationIntroduction">
import { computed } from 'vue';
import type { IData, IProps } from '../type/type';
import Tag from '@modules/exam-report/components/tag/index.vue';
import SubTitle from '@modules/exam-report/components/sub-title/index.vue';
import Progress from '@modules/exam-report/components/progress/index.vue';
import Card from '@modules/exam-report/components/card/index.vue';
import PageFooter from '@modules/exam-report/components/page-footer/index.vue';
import PageHeader from '@modules/exam-report/components/page-header/index.vue';
import SectionTitle from '@modules/exam-report/components/section-title/index.vue';
import { getBackgroundColor } from '../config';

const props = withDefaults(defineProps<IProps>(), {
    data: () => ({}) as IData,
    pageFooterData: () => [],
});

const splitArray = computed(() => {
    return [0, ...(props.data.levelArray || []), 10];
});

function getDimensionPerformance(value: string[]) {
    return value.join('\n');
}
</script>
<style lang="less" scoped>
.potential-qualities {
    &__description {
        line-height: var(--report-base-line-height-normal);
        white-space: pre-wrap;
        margin-bottom: var(--spacing-xlarge);
    }

    &__card {
        margin-bottom: var(--spacing-normal);
    }

    &__card-header {
        border-bottom-width: 1px;
        border-bottom-style: solid;
        border-bottom-color: var(--gray-color-2);
        margin-bottom: var(--spacing-medium);
        display: flex;
        align-items: center;
        padding-bottom: var(--spacing-xlarge);
    }

    &__card-title {
        min-width: 80px;
        margin-bottom: var(--spacing-none);
    }

    &__card-tag {
        margin-left: var(--spacing-small);
        margin-right: var(--spacing-xlarge);
    }

    &__card-define {
        color: var(--gray-color-6);
        line-height: var(--report-base-line-height-normal);
        margin-bottom: var(--spacing-normal);
    }

    &__card-performance {
        white-space: pre-wrap;
        line-height: var(--report-base-line-height-normal);
        display: flex;
    }
}
</style>
