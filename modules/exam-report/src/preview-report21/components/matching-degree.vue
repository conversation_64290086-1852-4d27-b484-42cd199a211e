<template>
    <PageHeader />
    <SectionTitle title="岗位素质模型匹配度" />
    <div class="matching-degree__description">
        {{ data.positionQualityModelMatchingDesc }}
    </div>
    <Table :tableData="data.positionQualityModelMatching || []" :columns="positionQualityModelMatchingColumns" :alternateBgColor="true">
        <template #td-dimensionName="{ row }">
            <SubTitle size="normal" type="title" class="matching-degree__dimension-name">{{ row.dimensionName }}</SubTitle>
        </template>
        <template #td-dimensionDefine="{ row }">
            <div class="matching-degree__dimension-define">{{ row.dimensionDefine }}</div>
        </template>
        <template #td-score="{ row }">
            <!-- <Progress
                class="matching-degree__process"
                :borderRadius="2"
                :footerConfig="false"
                :height="12"
                :minPartWidth="19"
                :partColors="['#F2F2F2']"
                :showTick="true"
                :splitArray="splitArray"
                :totalWidth="'100%'"
                type="grow"
                :value="row.score"
                :valueBarStyle="{ color: getBackgroundColor(row.level) }"
            >
                <template #value-indicator>
                    <div class="number" :style="{ '--bgc': getBackgroundColor(row.level) }">{{ row.score }}</div>
                </template>
            </Progress> -->
        </template>
    </Table>
    <PageFooter :data="pageFooterData" />
</template>

<script setup lang="ts" name="EvaluationIntroduction">
import { computed } from 'vue';
import type { IData, IProps } from '../type/type';
import SubTitle from '@modules/exam-report/components/sub-title/index.vue';
import PageFooter from '@modules/exam-report/components/page-footer/index.vue';
import PageHeader from '@modules/exam-report/components/page-header/index.vue';
import SectionTitle from '@modules/exam-report/components/section-title/index.vue';
import Table from '@modules/exam-report/components/table/index.vue';
import { positionQualityModelMatchingColumns, getBackgroundColor } from '../config';

const props = withDefaults(defineProps<IProps>(), {
    data: () => ({}) as IData,
    pageFooterData: () => [],
});
</script>
<style lang="less" scoped>
.matching-degree {
    &__description {
        line-height: var(--report-base-line-height-normal);
        white-space: pre-wrap;
        margin-bottom: var(--spacing-xlarge);
    }

    &__dimension-name {
        margin-bottom: var(--spacing-none);
    }

    &__dimension-define {
        white-space: pre-wrap;
        line-height: var(--report-base-line-height-normal);
    }
}
</style>
