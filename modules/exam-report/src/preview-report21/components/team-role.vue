<template>
    <PageHeader />
    <SectionTitle title="团队角色" />
    <div class="team-role__description">
        {{ data.teamRoleDesc }}
    </div>
    <Table :tableData="data.teamRoleList || []" :columns="teamRoleColumns" :alternateBgColor="true">
        <template #td-dimensionName="{ row }">
            <SubTitle size="normal" type="title" class="team-role__dimension-name">{{ row.dimensionName }}</SubTitle>
        </template>
        <template #td-dimensionDesc="{ row }">
            <div class="team-role__dimension-desc">{{ row.dimensionDesc }}</div>
        </template>
        <template #td-score="{ row }">
            <!-- <Progress
                class="matching-degree__process"
                :borderRadius="2"
                :footerConfig="false"
                :height="12"
                :minPartWidth="19"
                :partColors="['#F2F2F2']"
                :showTick="true"
                :splitArray="splitArray"
                :totalWidth="'100%'"
                type="grow"
                :value="row.score"
                :valueBarStyle="{ color: getBackgroundColor(row.level) }"
            >
                <template #value-indicator>
                    <div class="number" :style="{ '--bgc': getBackgroundColor(row.level) }">{{ row.score }}</div>
                </template>
            </Progress> -->
        </template>
    </Table>
    <div class="team-role__role-desc">
        作答者在团队中最可能成为的角色是：<span class="team-role__role-text">【{{ data.teamRole }}】</span>
    </div>
    <SubTitle size="large">潜在的优势</SubTitle>
    <div class="team-role__result">{{ data.potentialAdvantage }}</div>
    <SubTitle size="large">可能的局限</SubTitle>
    <div class="team-role__result">{{ data.possibleLimitation }}</div>
    <SubTitle size="large">角色管理建议</SubTitle>
    <div class="team-role__result">
        <SubTitle type="title" size="normal" class="team-role__result-title">
            <img src="https://img.bosszhipin.com/static/zhipin/kanjian/zhice/report/082851774257142482.svg" />用其所长
        </SubTitle>
        {{ data.useItsPotential }}
    </div>
    <div class="team-role__result">
        <SubTitle type="title" size="normal" class="team-role__result-title">
            <img src="https://img.bosszhipin.com/static/zhipin/kanjian/zhice/report/245031774257143013.svg" />规避不足
        </SubTitle>
        {{ data.avoidItsWeakness }}
    </div>
    <SubTitle size="large">团队搭配建议</SubTitle>
    <RichText v-if="data.teamCompatibilitySuggestion" :domString="data.teamCompatibilitySuggestion" :richTextIndex="1" />
    <PageFooter :data="pageFooterData" />
</template>

<script setup lang="ts" name="TeamRole">
import type { IData, IProps } from '../type/type';
import RichText from '@modules/exam-report/components/rich-text/index.vue';
import SubTitle from '@modules/exam-report/components/sub-title/index.vue';
import PageFooter from '@modules/exam-report/components/page-footer/index.vue';
import PageHeader from '@modules/exam-report/components/page-header/index.vue';
import SectionTitle from '@modules/exam-report/components/section-title/index.vue';
import Table from '@modules/exam-report/components/table/index.vue';
import { teamRoleColumns, getBackgroundColor } from '../config';

withDefaults(defineProps<IProps>(), {
    data: () => ({}) as IData,
    pageFooterData: () => [],
});
</script>
<style lang="less" scoped>
.team-role {
    &__description {
        line-height: var(--report-base-line-height-normal);
        white-space: pre-wrap;
        margin-bottom: var(--spacing-xlarge);
    }

    &__dimension-name {
        margin-bottom: var(--spacing-none);
    }

    &__dimension-desc {
        white-space: pre-wrap;
        line-height: var(--report-base-line-height-normal);
    }

    &__role-desc {
        background: #e8f7ff;
        border-color: #12a1fb;
        border-style: solid;
        border-width: 1px;
        border-radius: 123px;
        text-align: center;
        font-family: var(--FZLanTingHeiS-DB-GB);
        font-size: var(--report-base-font-size-large);
        line-height: 40px;
        margin-bottom: var(--spacing-xlarge);
        margin-top: var(--spacing-xlarge);
    }

    &__role-text {
        color: #12a1fb;
    }

    &__result {
        white-space: pre-wrap;
        line-height: var(--report-base-line-height-normal);
        margin-bottom: var(--spacing-xlarge);
    }

    &__result-title {
        margin-bottom: var(--spacing-none);
    }
}
</style>
