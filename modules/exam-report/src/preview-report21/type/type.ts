export enum ModuleEnum {
    Cover = '1',
    EvaluationIntroduction = '19',
    ComprehensiveResult = '3',
    PersonalityResultOverview = '20',
    PersonalityDetailResult = '21',
    KeyPotentialQualities = '22',
    MatchingDegree = '23',
    TeamRole = '24',
    InterviewSuggestions = '5',
    Reference = '6',
}

export enum LevelEnum {
    High = 3,
    Medium = 2,
    Low = 1,
}

export interface ISubDimensionItem {
    dimensionName: string;
    showName: string;
    definition: string;
    level: number;
    score: number | string;
    levelStr: string;
    levelDescription: string;
}

type LevelType = LevelEnum;

export interface IExamineeInfoItem {
    encryptFieldId: string;
    fieldName: string;
    fieldShowName: string;
    fieldValue: string;
}

export interface IIndicativeItem {
    encryptIndicatorId: string;
    indicatorName: string;
    showName: string;
    indicatorMeaning: string;
    result: string;
}

export interface IInterviewSuggestionsItem {
    dimensionName: string;
    showName: string;
    score: number;
    level: number;
    interviewQuestions: string;
    keyPoints: string;
}

export interface IPotentialQualitiesItem {
    dimensionName: string;
    dimensionDefine: string;
    score: number;
    level: number;
    levelStr: string;
    dimensionDesc: string;
    dimensionPerformanceList: string[];
}

export interface IPositionQualityModelMatchingItem {
    dimensionName: string;
    dimensionDefine: string;
    score: number;
    level: number;
    levelStr: string;
}

export interface ITeamRoleItem {
    dimensionName: string;
    dimensionDesc: string;
    score: number;
    level: number;
    levelStr: string;
}

export interface IData extends Record<string, any> {
    examineeInfoList: IExamineeInfoItem[];
    testDescription: string;
    potentialQualitiesKeyDesc: string;
    positionQualityModelMatchingDesc: string;
    teamRoleDesc: string;
    potentialQualities: IPotentialQualitiesItem[];
    positionQualityModelMatching: IPositionQualityModelMatchingItem[];
    teamRoleList: ITeamRoleItem[];
    teamRole: string;
    potentialAdvantage: string;
    possibleLimitation: string;
    avoidItsWeakness: string;
    useItsPotential: string;
    teamCompatibilitySuggestion: string;
    levelArray: number[];
    // indicativeResultLevel: number;
    // indicativeResultDesc: string;
    // usageInstructions: string;
    // indicativeList: IIndicativeItem[];
    // readingInstructions: string;
    // interviewSuggestions: IInterviewSuggestionsItem[];
}

export interface IProps {
    data?: IData;
    pageFooterData?: string[];
    headerFooterConfig?: { header: boolean; footer: boolean };
}

export interface IExamineeInfo {
    fieldShowName: string;
    fieldValue: string;
}

export interface ICoverData {
    templateName: string;
    reportSubTitle: string;
    examineeInfoList: IExamineeInfo[];
}

type TAlignType = 'left' | 'center' | 'right';

export interface ITableColumn {
    field: string;
    label: string;
    width?: number | string;
    align?: TAlignType;
    headerWidth?: number | string;
    children?: ITableColumn[];
    rowSpan?: Record<string, any>;
}

type TStatusType = 'info' | 'success' | 'danger' | 'warning';

export interface ILevelConfig {
    color: string;
    backgroundColor: string;
    tagBackgroundColor: string;
    cardBackgroundColor: string;
}
