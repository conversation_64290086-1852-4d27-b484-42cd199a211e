export interface IColumn {
    field: string;
    label: string;
    width?: number | string;
    align?: 'left' | 'center' | 'right';
    children?: Array<IColumn>;
    headerWidth?: number | string;
    rowSpan?: Record<string, any>;
}
export interface Props {
    columns: Array<IColumn>;
    tableData: Array<any>;
    border?: boolean;
    type?: 'normal' | 'simple';
    alternateBgColor?: boolean;
}
