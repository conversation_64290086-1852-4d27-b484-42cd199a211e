<template>
    <b-layout type="content">
        <b-layout direction="vertical">
            <div class="form-section">
                <b-section type="title" :bgColor="true">
                    <b-title type="preline" size="small"> 五维性格测评 2.0 </b-title>
                </b-section>
                <TabLayout
                    v-model="currentTab"
                    v-loading="detailLoading"
                    :showFooter="currentTab === 1"
                    :loading="loading"
                    :tabList="[
                        { label: '参数设置', value: 1 },
                        { label: '计分规则导入', value: 2 },
                        { label: '企业定制模型', value: 3 },
                    ]"
                    @cancel="cancel"
                    @save="save"
                >
                    <!-- 参数设置 -->
                    <div v-if="currentTab === 1" class="form-section">
                        <!-- 基础参数 -->
                        <div class="sub-title">
                            <h4>基础参数</h4>
                        </div>
                        <b-form ref="formRef" scrollToFirstError :model="queryData" useGrid :gridProps="{ gap: 10, columns: 2, rows: 3 }" layout="inline" @submit.prevent>
                            <b-form-item label="参数a" field="paramA" asteriskPosition="end" :rules="[{ required: true, type: 'number', message: '请填写参数a' }]">
                                <b-input-number v-model.trim="queryData.paramA" placeholder="请填写1-99内整数" hideButton :min="1" :max="99" :precision="0" />
                            </b-form-item>
                            <b-form-item label="参数b" field="paramB" asteriskPosition="end" :rules="[{ required: true, type: 'number', message: '请填写参数b' }]">
                                <b-input-number v-model.trim="queryData.paramB" placeholder="请填写1-99内整数" hideButton :min="1" :max="99" :precision="0" />
                            </b-form-item>
                            <b-form-item
                                label="得分下限"
                                field="scoreMin"
                                asteriskPosition="end"
                                :rules="[
                                    { required: true, type: 'number', message: '请填写得分下限' },
                                    { validator: (value, callback) => formValidator(value, callback, 'scoreMin') },
                                ]"
                            >
                                <b-input-number
                                    v-model.trim="queryData.scoreMin"
                                    placeholder="请填写1-99内整数"
                                    hideButton
                                    :min="1"
                                    :max="99"
                                    :precision="0"
                                    @change="handleChange('base')"
                                />
                            </b-form-item>
                            <b-form-item
                                label="得分上限"
                                field="scoreMax"
                                asteriskPosition="end"
                                :rules="[
                                    { required: true, type: 'number', message: '请填写得分上限' },
                                    { validator: (value, callback) => formValidator(value, callback, 'scoreMax') },
                                ]"
                            >
                                <b-input-number
                                    v-model.trim="queryData.scoreMax"
                                    placeholder="请填写1-99内整数"
                                    hideButton
                                    :min="1"
                                    :max="99"
                                    :precision="0"
                                    @change="handleChange('base')"
                                />
                            </b-form-item>
                        </b-form>

                        <!-- 等级划分 -->
                        <div class="sub-title">
                            <h4>等级划分</h4>
                        </div>
                        <b-form
                            ref="levelFormRef"
                            scrollToFirstError
                            class="level-form"
                            :model="{ levels: formLevelData }"
                            useGrid
                            :gridProps="{ gap: [20, 10], columns: 5 }"
                            layout="inline"
                            @submit.prevent
                        >
                            <template v-for="(item, index) in LEVEL_LABELS" :key="index">
                                <b-form-item
                                    :field="`levels.${index}`"
                                    :label="item.label"
                                    asteriskPosition="end"
                                    :rules="!item.hide ? [{ validator: (value, callback) => formLevelValidator(value, callback, index) }] : []"
                                >
                                    <template v-if="!item.hide">
                                        <b-input-number
                                            v-model.trim="formLevelData[index]"
                                            @change="levelFormRef.validate()"
                                            class="dimension-level"
                                            placeholder="请输入"
                                            hideButton
                                            :min="0.1"
                                            :max="99.9"
                                            :precision="1"
                                        />
                                    </template>
                                </b-form-item>
                            </template>
                        </b-form>

                        <!-- 关键潜在素质分数计算 -->
                        <KeyPotentialQualityConfig ref="keyPotentialRef" v-model="keyPotentialQualityData" />

                        <!-- 岗位素质模型匹配度分数计算 -->
                        <JobQualityModelConfig ref="jobQualityRef" v-model="jobQualityModelData" />

                        <!-- 团队角色分数计算 -->
                        <TeamRoleConfig ref="teamRoleRef" v-model="teamRoleData" />
                    </div>

                    <!-- 计分规则导入 -->
                    <ScoreRuleConfig v-else-if="currentTab === 2" :tableData="exportTableData" />

                    <!-- 企业定制模型 -->
                    <EnterpriseCustomModelConfig v-else />
                </TabLayout>
            </div>
        </b-layout>
    </b-layout>
</template>

<script setup lang="ts">
import type { ApiConfigItem, Character2BaseParams } from '@/types/character2';
import { CHARACTER2_CONSTANTS } from '@/types/character2';
import EnterpriseCustomModelConfig from '../components/enterprise-custom-model-config.vue';
import { pushRouter } from '@/router-v2';
import { AdminModuleCode } from '@/router-v2/types';
import { onMounted, onUnmounted, ref, watch, computed } from 'vue';
import { onBeforeRouteLeave, useRoute } from 'vue-router';
import { useCharacter2Store } from '@/stores/character2';
import { useCharacter2Validation } from '@/composables/useCharacter2Validation';

// 全局变量声明
declare const Toast: {
    success: (message: string) => void;
    danger: (message: string) => void;
};

declare const Dialog: {
    open: (options: { type: string; title: string; content: string; confirm: () => void; cancel?: () => void }) => void;
};

import ScoreRuleConfig from '../components/score-rule-config.vue';
import TabLayout from '../components/tab-layout.vue';
import KeyPotentialQualityConfig from '../components/key-potential-quality-config.vue';
import JobQualityModelConfig from '../components/job-quality-model-config.vue';
import TeamRoleConfigComponent from '../components/team-role-config.vue';

defineOptions({
    name: 'Character2Detail',
});

const $route = useRoute();
const store = useCharacter2Store();

// 表单引用
const formRef = ref();
const levelFormRef = ref();

// 子组件引用
const keyPotentialRef = ref();
const jobQualityRef = ref();
const teamRoleRef = ref();

// 使用验证组合式函数
const { validateForm: validateBaseForm } = useCharacter2Validation(formRef);

// 等级标签配置
const LEVEL_LABELS = [
    { label: '低 <', hide: false },
    { label: '≤ 较低 <', hide: false },
    { label: '≤ 中 <', hide: false },
    { label: '≤ 较高 <', hide: false },
    { label: '≤ 高', hide: true },
];

// 基础参数数据 - 使用 store 中的数据
const queryData = computed({
    get: () => store.baseParams,
    set: (value: Character2BaseParams) => store.updateBaseParams(value),
});

// 等级划分数据 - 使用 store 中的数据
const formLevelData = computed({
    get: () => store.levelData,
    set: (value: number[]) => store.updateLevelData(value),
});

// 关键潜在素质数据 - 使用 store 中的数据
const keyPotentialQualityData = computed({
    get: () => store.keyPotentialQualityData,
    set: (value: ApiConfigItem[]) => store.updateKeyPotentialQualityData(value),
});

// 岗位素质模型数据 - 使用 store 中的数据
const jobQualityModelData = computed({
    get: () => store.jobQualityModelData,
    set: (value: ApiConfigItem[]) => store.updateJobQualityModelData(value),
});

// 团队角色数据 - 使用 store 中的数据
const teamRoleData = computed({
    get: () => store.teamRoleData,
    set: (value: any[]) => store.updateTeamRoleData(value),
});

// 导出表格数据
const exportTableData = ref<any[]>([]);

// 当前标签页
const currentTab = computed({
    get: () => store.currentTab,
    set: (value: number) => store.setCurrentTab(value),
});

// 加载状态
const loading = computed(() => store.formState.loading);
const saved = computed(() => store.saved);
const dataChanged = computed(() => store.dataChanged);

// 表单验证
function formValidator(value: number, callback: (message?: string) => void, filed: string) {
    if (value || value === 0) {
        if (filed === 'scoreMax' && store.baseParams.scoreMin !== undefined && value <= store.baseParams.scoreMin) {
            callback('得分上限必须大于得分下限');
        }
        return;
    }
    callback();
}

function formLevelValidator(value: number, callback: (message?: string) => void, index: number) {
    if (!(value || value === 0)) {
        return callback('请填写等级划分');
    }
    if (index < LEVEL_LABELS.length - 1 && store.levelData[index + 1] !== undefined && value >= store.levelData[index + 1]) {
        return callback('数值必须小于右侧值');
    }
    return callback();
}

function handleChange(key: string) {
    if (key === 'base') {
        formRef.value.validateField(['scoreMax']);
    }
}
async function save() {
    try {
        // 校验基础表单
        const formRes = await formRef.value.validate();
        const levelRes = await levelFormRef.value.validate();

        // 校验子组件表单
        let keyPotentialRes = true;
        let jobQualityRes = true;
        let teamRoleRes = true;

        try {
            if (keyPotentialRef.value?.validate) {
                keyPotentialRes = await keyPotentialRef.value.validate();
            }
            if (jobQualityRef.value?.validate) {
                jobQualityRes = await jobQualityRef.value.validate();
            }
            if (teamRoleRef.value?.validate) {
                teamRoleRes = await teamRoleRef.value.validate();
            }
        } catch (error) {
            console.error('子组件校验失败:', error);
            Toast.danger('表单校验失败，请检查填写内容');
            return;
        }

        // 如果任何表单校验失败，则不继续保存
        if (formRes || levelRes || !keyPotentialRes || !jobQualityRes || !teamRoleRes) {
            Toast.danger('请完善表单信息后再保存');
            return;
        }

        // 使用 store 的保存方法
        const success = await store.saveData($route.query?.productId as string);

        if (success) {
            Toast.success('保存成功');
            cancel();
        }
    } catch (e: any) {
        Toast.danger(e?.message || '保存失败');
    }
}

function cancel() {
    pushRouter(AdminModuleCode.evaluationScoring.root);
}

// 数据初始化
const detailLoading = computed(() => store.formState.loading);

async function initializeData() {
    try {
        await store.loadDetailData($route.query?.productId as string);

        // 处理导出表格数据 - 这部分逻辑保持不变
        // 因为它不在 store 中管理
        // TODO: 如果需要，可以将这部分也移到 store 中
    } catch (e: any) {
        Toast.danger(e?.message || '加载数据失败');
    }
}

// 页面加载时初始化数据
initializeData();

// 数据变更监听
function startWatchData() {
    watch(
        () => store.hasUnsavedChanges,
        (hasChanges) => {
            if (hasChanges) {
                window.onbeforeunload = function () {
                    return '确认离开此页面吗？';
                };
            } else {
                window.onbeforeunload = null;
            }
        }
    );
}

onMounted(() => {
    startWatchData();
});

onUnmounted(() => {
    window.onbeforeunload = null;
});

onBeforeRouteLeave((to, _from, next) => {
    if (to.path === AdminModuleCode.evaluationScoring.root) {
        to.query.resetPage = '1';
    }

    if (store.saved) {
        next();
    } else {
        if (store.hasUnsavedChanges) {
            Dialog.open({
                type: 'warning',
                title: '离开提示',
                content: '当前数据尚未保存，确定要离开吗？',
                confirm() {
                    next();
                },
                cancel() {
                    next(false);
                },
            });
        } else {
            next();
        }
    }
});
</script>

<style lang="less" scoped>
@import '@/styles/evaluation/scoring/character-detail.less';

.sub-title {
    margin: 20px 0 10px 0;

    h4 {
        font-size: 14px;
        font-weight: 600;
        color: #1d2129;
        margin: 0;
    }
}

.level-form {
    margin-bottom: 20px;
}

.dimension-level {
    width: 100%;
}

.config-placeholder {
    padding: 40px;
    text-align: center;
    color: #86909c;
    font-size: 14px;
    background: #f7f8fa;
    border-radius: 4px;
    margin: 20px 0;
}
</style>
