<template>
    <div class="data-echo-test">
        <h3>数据回显测试页面</h3>
        
        <div class="test-section">
            <h4>模拟API数据：</h4>
            <div class="action-buttons">
                <b-button type="primary" @click="loadTestData">加载测试数据</b-button>
                <b-button @click="clearData">清空数据</b-button>
            </div>
            <pre v-if="testApiData">{{ JSON.stringify(testApiData, null, 2) }}</pre>
        </div>
        
        <div class="test-section">
            <h4>关键潜在素质配置：</h4>
            <KeyPotentialQualityConfig ref="keyPotentialRef" v-model="keyPotentialData" />
        </div>
        
        <div class="test-section">
            <h4>岗位素质模型配置：</h4>
            <JobQualityModelConfig ref="jobQualityRef" v-model="jobQualityData" />
        </div>
        
        <div class="test-section">
            <h4>团队角色配置（参考）：</h4>
            <TeamRoleConfig ref="teamRoleRef" v-model="teamRoleData" />
        </div>
        
        <div class="action-buttons">
            <b-button type="success" @click="testDataEcho">测试数据回显</b-button>
            <b-button @click="getCurrentData">获取当前数据</b-button>
        </div>
        
        <div v-if="testResults" class="test-results">
            <h4>测试结果：</h4>
            <pre>{{ testResults }}</pre>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import KeyPotentialQualityConfig from './key-potential-quality-config.vue';
import JobQualityModelConfig from './job-quality-model-config.vue';
import TeamRoleConfig from './team-role-config.vue';

// 组件引用
const keyPotentialRef = ref();
const jobQualityRef = ref();
const teamRoleRef = ref();

// 数据模型
const keyPotentialData = ref([]);
const jobQualityData = ref([]);
const teamRoleData = ref([]);

// 测试数据和结果
const testApiData = ref(null);
const testResults = ref('');

// 模拟API返回的测试数据
function loadTestData() {
    // 模拟关键潜在素质数据
    const mockKeyPotentialData = [
        {
            encId: "group1",
            headName: "配置组1",
            rowDataList: [
                {
                    encDimensionId: "dim1",
                    dimensionName: "A-1-彦祖二级维度",
                    define: null,
                    weight: 1,
                    normalAverageScore: 50,
                    normalStandardDeviation: 10
                },
                {
                    encDimensionId: "dim2", 
                    dimensionName: "B-1-二级维度",
                    define: null,
                    weight: 0.5,
                    normalAverageScore: 60,
                    normalStandardDeviation: 12
                }
            ]
        },
        {
            encId: "group2",
            headName: "配置组2", 
            rowDataList: [
                {
                    encDimensionId: "dim1",
                    dimensionName: "A-1-彦祖二级维度",
                    define: null,
                    weight: 0.8,
                    normalAverageScore: 55,
                    normalStandardDeviation: 8
                },
                {
                    encDimensionId: "dim2",
                    dimensionName: "B-1-二级维度", 
                    define: null,
                    weight: 0.3,
                    normalAverageScore: 65,
                    normalStandardDeviation: 15
                }
            ]
        }
    ];

    // 模拟岗位素质模型数据
    const mockJobQualityData = [
        {
            encId: "job_group1",
            headName: "岗位配置组1",
            rowDataList: [
                {
                    encDimensionId: "dim1",
                    dimensionName: "A-1-彦祖二级维度",
                    define: null,
                    weight: 0.7,
                    normalAverageScore: 45,
                    normalStandardDeviation: 9
                },
                {
                    encDimensionId: "dim2",
                    dimensionName: "B-1-二级维度",
                    define: null,
                    weight: 0.4,
                    normalAverageScore: 70,
                    normalStandardDeviation: 11
                }
            ]
        }
    ];

    // 模拟团队角色数据
    const mockTeamRoleData = [
        {
            encId: "",
            headName: "团队角色",
            rowDataList: [
                {
                    encDimensionId: "1",
                    dimensionName: "实干者 1111",
                    weight: 1,
                    normalAverageScore: 50,
                    normalStandardDeviation: 10
                },
                {
                    encDimensionId: "2", 
                    dimensionName: "协调者",
                    weight: 0.2,
                    normalAverageScore: 60,
                    normalStandardDeviation: 12
                }
                // ... 其他7个角色
            ]
        }
    ];

    testApiData.value = {
        keyPotentialQualityList: mockKeyPotentialData,
        positionQualityModelMatchList: mockJobQualityData,
        teamRoleList: mockTeamRoleData
    };

    // 设置数据到组件
    keyPotentialData.value = mockKeyPotentialData;
    jobQualityData.value = mockJobQualityData;
    teamRoleData.value = mockTeamRoleData;

    testResults.value = '测试数据已加载，请检查组件中的数据回显情况';
}

// 清空数据
function clearData() {
    keyPotentialData.value = [];
    jobQualityData.value = [];
    teamRoleData.value = [];
    testApiData.value = null;
    testResults.value = '';
}

// 测试数据回显
function testDataEcho() {
    const keyPotentialFormData = keyPotentialRef.value?.getCurrentFormData();
    const jobQualityFormData = jobQualityRef.value?.getCurrentFormData();
    
    testResults.value = JSON.stringify({
        keyPotentialFormData,
        jobQualityFormData,
        timestamp: new Date().toISOString()
    }, null, 2);
}

// 获取当前数据
function getCurrentData() {
    testResults.value = JSON.stringify({
        keyPotentialData: keyPotentialData.value,
        jobQualityData: jobQualityData.value,
        teamRoleData: teamRoleData.value,
        timestamp: new Date().toISOString()
    }, null, 2);
}
</script>

<style lang="less" scoped>
.data-echo-test {
    padding: 20px;
    max-width: 1400px;
}

.test-section {
    margin: 30px 0;
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 4px;
    
    h4 {
        margin-top: 0;
        color: #333;
    }
    
    pre {
        background-color: #f5f5f5;
        padding: 10px;
        border-radius: 4px;
        overflow-x: auto;
        font-size: 12px;
        max-height: 300px;
        overflow-y: auto;
    }
}

.action-buttons {
    margin: 15px 0;
    display: flex;
    gap: 10px;
}

.test-results {
    margin-top: 20px;
    padding: 15px;
    background-color: #f0f8ff;
    border-radius: 4px;
    
    h4 {
        margin-top: 0;
        color: #333;
    }
    
    pre {
        background-color: #fff;
        padding: 15px;
        border-radius: 4px;
        border: 1px solid #ddd;
        overflow-x: auto;
        font-size: 12px;
        max-height: 500px;
        overflow-y: auto;
    }
}
</style>
