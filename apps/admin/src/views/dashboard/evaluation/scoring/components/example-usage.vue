<template>
    <div class="example-usage">
        <h3>表单校验示例</h3>
        
        <!-- 关键潜在素质配置 -->
        <KeyPotentialQualityConfig 
            ref="keyPotentialRef"
            v-model="keyPotentialData"
        />
        
        <!-- 岗位素质模型配置 -->
        <JobQualityModelConfig 
            ref="jobQualityRef"
            v-model="jobQualityData"
        />
        
        <!-- 团队角色配置（新版） -->
        <TeamRoleConfigNew 
            ref="teamRoleNewRef"
            v-model="teamRoleNewData"
        />
        
        <!-- 团队角色配置（旧版） -->
        <TeamRoleConfig 
            ref="teamRoleRef"
            v-model="teamRoleData"
        />
        
        <div class="action-buttons">
            <b-button type="primary" @click="validateAll">校验所有表单</b-button>
            <b-button @click="validateSingle">校验单个表单</b-button>
            <b-button type="success" @click="submitAll">提交所有数据</b-button>
        </div>
        
        <div v-if="validationResult" class="validation-result">
            <h4>校验结果：</h4>
            <pre>{{ validationResult }}</pre>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import KeyPotentialQualityConfig from './key-potential-quality-config.vue';
import JobQualityModelConfig from './job-quality-model-config.vue';
import TeamRoleConfigNew from './team-role-config-new.vue';
import TeamRoleConfig from './team-role-config.vue';

// 组件引用
const keyPotentialRef = ref();
const jobQualityRef = ref();
const teamRoleNewRef = ref();
const teamRoleRef = ref();

// 数据模型
const keyPotentialData = ref({});
const jobQualityData = ref({});
const teamRoleNewData = ref({});
const teamRoleData = ref({});

// 校验结果
const validationResult = ref('');

// 校验所有表单
async function validateAll() {
    try {
        const results = await Promise.all([
            keyPotentialRef.value?.validate(),
            jobQualityRef.value?.validate(),
            teamRoleNewRef.value?.validate(),
            teamRoleRef.value?.validate(),
        ]);
        
        const allValid = results.every(result => result === true);
        
        validationResult.value = JSON.stringify({
            allValid,
            results: {
                keyPotential: results[0],
                jobQuality: results[1],
                teamRoleNew: results[2],
                teamRole: results[3],
            }
        }, null, 2);
        
        if (allValid) {
            console.log('所有表单校验通过');
        } else {
            console.log('存在校验失败的表单');
        }
    } catch (error) {
        console.error('校验过程中出现错误:', error);
        validationResult.value = `校验错误: ${error}`;
    }
}

// 校验单个表单（示例：校验关键潜在素质配置）
async function validateSingle() {
    try {
        const result = await keyPotentialRef.value?.validate();
        validationResult.value = JSON.stringify({
            component: 'KeyPotentialQualityConfig',
            valid: result
        }, null, 2);
    } catch (error) {
        console.error('单个表单校验错误:', error);
        validationResult.value = `单个表单校验错误: ${error}`;
    }
}

// 提交所有数据
async function submitAll() {
    const allValid = await validateAll();
    if (allValid) {
        // 这里可以调用API提交数据
        console.log('提交数据:', {
            keyPotential: keyPotentialData.value,
            jobQuality: jobQualityData.value,
            teamRoleNew: teamRoleNewData.value,
            teamRole: teamRoleData.value,
        });
    } else {
        console.log('表单校验失败，无法提交');
    }
}
</script>

<style lang="less" scoped>
.example-usage {
    padding: 20px;
}

.action-buttons {
    margin: 20px 0;
    display: flex;
    gap: 10px;
}

.validation-result {
    margin-top: 20px;
    padding: 15px;
    background-color: #f5f5f5;
    border-radius: 4px;
    
    h4 {
        margin-top: 0;
        color: #333;
    }
    
    pre {
        background-color: #fff;
        padding: 10px;
        border-radius: 4px;
        border: 1px solid #ddd;
        overflow-x: auto;
    }
}
</style>
