<template>
    <div class="data-flow-test">
        <h3>数据流测试页面</h3>
        
        <div class="test-section">
            <h4>父组件数据状态：</h4>
            <pre>{{ JSON.stringify({ keyPotentialQualityData, jobQualityModelData, teamRoleData }, null, 2) }}</pre>
        </div>
        
        <div class="test-section">
            <h4>团队角色配置组件：</h4>
            <TeamRoleConfig 
                ref="teamRoleRef"
                v-model="teamRoleData"
            />
        </div>
        
        <div class="action-buttons">
            <b-button type="primary" @click="validateForm">校验表单</b-button>
            <b-button @click="initTestData">初始化测试数据</b-button>
            <b-button @click="clearData">清空数据</b-button>
        </div>
        
        <div v-if="validationResult" class="validation-result">
            <h4>校验结果：</h4>
            <pre>{{ validationResult }}</pre>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue';
import TeamRoleConfig from './team-role-config.vue';

// 组件引用
const teamRoleRef = ref();

// 数据模型 - 模拟父组件的数据结构
const keyPotentialQualityData = ref([]);
const jobQualityModelData = ref([]);
const teamRoleData = ref([]);

// 校验结果
const validationResult = ref('');

// 校验表单
async function validateForm() {
    try {
        const result = await teamRoleRef.value?.validate();
        validationResult.value = JSON.stringify({
            component: 'TeamRoleConfig',
            valid: result,
            timestamp: new Date().toISOString()
        }, null, 2);
    } catch (error) {
        console.error('校验错误:', error);
        validationResult.value = `校验错误: ${error}`;
    }
}

// 初始化测试数据
function initTestData() {
    teamRoleData.value = [
        {
            encId: 'test1',
            headName: '团队角色',
            rowDataList: [
                {
                    encDimensionId: '1',
                    dimensionName: '实干者',
                    weight: 0.1,
                    normalAverageScore: 50,
                    normalStandardDeviation: 10,
                },
                {
                    encDimensionId: '2',
                    dimensionName: '协调者',
                    weight: 0.1,
                    normalAverageScore: 55,
                    normalStandardDeviation: 12,
                },
                // 添加更多测试数据...
            ]
        }
    ];
    
    validationResult.value = '测试数据已初始化';
}

// 清空数据
function clearData() {
    teamRoleData.value = [];
    keyPotentialQualityData.value = [];
    jobQualityModelData.value = [];
    validationResult.value = '数据已清空';
}
</script>

<style lang="less" scoped>
.data-flow-test {
    padding: 20px;
}

.test-section {
    margin: 20px 0;
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
    
    h4 {
        margin-top: 0;
        color: #333;
    }
    
    pre {
        background-color: #f5f5f5;
        padding: 10px;
        border-radius: 4px;
        overflow-x: auto;
        font-size: 12px;
    }
}

.action-buttons {
    margin: 20px 0;
    display: flex;
    gap: 10px;
}

.validation-result {
    margin-top: 20px;
    padding: 15px;
    background-color: #f0f8ff;
    border-radius: 4px;
    
    h4 {
        margin-top: 0;
        color: #333;
    }
    
    pre {
        background-color: #fff;
        padding: 10px;
        border-radius: 4px;
        border: 1px solid #ddd;
        overflow-x: auto;
    }
}
</style>
