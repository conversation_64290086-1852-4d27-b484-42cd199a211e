<template>
    <div class="team-role-config">
        <b-form ref="formRef" :model="{ weightList, normList }" layout="vertical" :scrollToFirstError="{ block: 'center' }">
            <div class="sub-title">
                <h4>团队角色分数计算</h4>
            </div>

            <!-- 团队角色分数权重表 -->
            <div class="section-title">
                <h5>团队角色分数权重表</h5>
            </div>

            <b-table border fitWidth :columns="weightColumns" :tableData="weightList">
                <template v-for="(col, index) in weightColumns" :key="col.field" #[`th-${col.field}`]>
                    <div v-if="index === 0" class="reference">
                        <div style="white-space: nowrap">团队角色名称</div>
                    </div>
                    <div v-else class="reference">
                        <div style="white-space: nowrap">权重</div>
                    </div>
                </template>
                <template v-for="(col, index) in weightColumns" :key="col.field" #[`td-${col.field}`]="{ raw, $index }">
                    <div v-if="index === 0">
                        <FormField :field="`weightList.${$index}.name`" hideLabel :rules="[{ required: true, message: '请输入团队角色名称' }]">
                            <b-input v-model="raw.name" placeholder="请输入团队角色名称" />
                        </FormField>
                    </div>
                    <div v-else>
                        <div>
                            <FormField :field="`weightList.${$index}.weight`" hideLabel :rules="[{ required: true, message: '请输入权重' }]">
                                <b-input-number v-model="raw.weight" placeholder="请输入权重" hideButton :min="0" :max="1" :precision="4" @change="validateWeightSum" />
                            </FormField>
                        </div>
                    </div>
                </template>
            </b-table>

            <div v-if="weightSumError" class="error-tip">权重和必须等于 1</div>

            <!-- 团队角色常模 -->
            <div class="section-title">
                <h5>团队角色常模</h5>
            </div>

            <b-table border fitWidth :columns="normColumns" :tableData="normList">
                <template v-for="(col, index) in normColumns" :key="col.field" #[`th-${col.field}`]>
                    <div v-if="index === 0" class="reference">
                        <div style="white-space: nowrap">团队角色名称</div>
                    </div>
                    <div v-else-if="index === 1" class="reference">
                        <div style="white-space: nowrap">常模平均分</div>
                    </div>
                    <div v-else class="reference">
                        <div style="white-space: nowrap">常模标准差</div>
                    </div>
                </template>
                <template v-for="(col, index) in normColumns" :key="col.field" #[`td-${col.field}`]="{ raw, $index }">
                    <div v-if="index === 0">
                        {{ raw.name }}
                    </div>
                    <div v-else-if="index === 1" class="center">
                        <div>
                            <FormField :field="`normList.${$index}.avgScore`" hideLabel :rules="[{ required: true, message: '请填写常模平均分' }]">
                                <b-input-number
                                    v-model="raw.avgScore"
                                    style="width: 100%"
                                    placeholder="请填写0-1000内数值，最多4位小数"
                                    hideButton
                                    :min="0"
                                    :max="1000"
                                    :precision="4"
                                />
                            </FormField>
                        </div>
                    </div>
                    <div v-else class="center">
                        <div>
                            <FormField :field="`normList.${$index}.stdDev`" hideLabel :rules="[{ required: true, message: '请填写常模标准差' }]">
                                <b-input-number v-model="raw.stdDev" placeholder="请填写1000以内正数，最多4位小数" hideButton :min="0.0001" :max="1000" :precision="4" />
                            </FormField>
                        </div>
                    </div>
                </template>
            </b-table>
        </b-form>
    </div>
</template>

<script setup lang="ts">
import { ref, watch, nextTick } from 'vue';
import type { _FormComponent } from '@boss/design';
import { FormField } from '@crm/web-components';
import { useFormValidator } from '@crm/vueuse-pro';

interface WeightItem {
    id: string;
    name: string;
    weight: number | undefined;
}

interface NormItem {
    id: string;
    name: string;
    avgScore: number | undefined;
    stdDev: number | undefined;
}

const props = defineProps<{
    modelValue: any;
}>();

const emit = defineEmits<{
    'update:modelValue': [value: any];
    validate: [callback: () => Promise<boolean>];
}>();

// 表单相关
const formRef = ref<_FormComponent>();

// 表单校验
const { validateAll } = useFormValidator({
    formRef,
    customValidate: async () => {
        // 自定义校验逻辑
        return !weightSumError.value;
    },
});

// 暴露校验方法给父组件
defineExpose({
    validate: validateAll,
});

// 默认的九个团队角色
const DEFAULT_TEAM_ROLES = ['实干者', '协调者', '推进者', '创新者', '信息者', '监督者', '凝聚者', '完善者', '专业师'];

// 权重表数据 - 固定九个角色
const weightList = ref<WeightItem[]>(
    DEFAULT_TEAM_ROLES.map((name, index) => ({
        id: (index + 1).toString(),
        name,
        weight: undefined,
    }))
);

// 常模数据 - 对应九个角色
const normList = ref<NormItem[]>(
    DEFAULT_TEAM_ROLES.map((name, index) => ({
        id: (index + 1).toString(),
        name,
        avgScore: undefined,
        stdDev: undefined,
    }))
);

// 权重表列配置
const weightColumns = [
    {
        label: '团队角色名称',
        field: 'name',
        width: 200,
        thName: 'th-name',
        tdName: 'td-name',
    },
    {
        label: '权重',
        field: 'weight',
        width: 150,
        thName: 'th-weight',
        tdName: 'td-weight',
    },
];

// 常模表列配置
const normColumns = [
    {
        label: '团队角色名称',
        field: 'name',
        width: 200,
        thName: 'th-name',
        tdName: 'td-name',
    },
    {
        label: '常模平均分',
        field: 'avgScore',
        width: 150,
        thName: 'th-avgScore',
        tdName: 'td-avgScore',
    },
    {
        label: '常模标准差',
        field: 'stdDev',
        width: 150,
        thName: 'th-stdDev',
        tdName: 'td-stdDev',
    },
];

// 权重和验证
const weightSumError = ref(false);

function validateWeightSum() {
    const sum = weightList.value.reduce((acc, item) => acc + (item.weight || 0), 0);
    weightSumError.value = Math.abs(sum - 1) > 0.0001;
}

// 监听权重表名称变化，同步到常模表
watch(
    () => weightList.value.map((item) => ({ id: item.id, name: item.name })),
    (newNames) => {
        // 如果正在从父组件更新，则不触发同步
        if (isUpdatingFromParent.value) {
            return;
        }

        newNames.forEach((item, index) => {
            if (normList.value[index]) {
                normList.value[index].name = item.name;
            }
        });
    },
    { deep: true }
);

// 防止循环更新的标志
const isUpdatingFromParent = ref(false);

// 监听数据变化，向父组件发送更新
watch(
    [weightList, normList],
    () => {
        // 如果正在从父组件更新，则不触发向上的更新
        if (isUpdatingFromParent.value) {
            return;
        }

        // 构建符合API数据结构的格式
        const teamRoleList = [
            {
                encId: '', // 可以根据需要设置
                headName: '团队角色',
                rowDataList: weightList.value.map((item, index) => ({
                    encDimensionId: item.id || '',
                    dimensionName: item.name || '',
                    weight: item.weight || 0,
                    normalAverageScore: normList.value[index]?.avgScore || 0,
                    normalStandardDeviation: normList.value[index]?.stdDev || 0,
                })),
            },
        ];

        emit('update:modelValue', teamRoleList);
    },
    { deep: true }
);

// 初始化数据
watch(
    () => props.modelValue,
    (newValue) => {
        // 设置标志，防止循环更新
        isUpdatingFromParent.value = true;

        try {
            if (Array.isArray(newValue) && newValue.length > 0) {
                const firstItem = newValue[0];
                if (firstItem?.rowDataList && firstItem.rowDataList.length === 9) {
                    weightList.value = firstItem.rowDataList.map((row: any) => ({
                        id: row.encDimensionId || Date.now().toString(),
                        name: row.dimensionName || '',
                        weight: row.weight || 0,
                    }));

                    normList.value = firstItem.rowDataList.map((row: any) => ({
                        id: row.encDimensionId || Date.now().toString(),
                        name: row.dimensionName || '',
                        avgScore: row.normalAverageScore || 0,
                        stdDev: row.normalStandardDeviation || 0,
                    }));
                }
            } else if (newValue?.weightList && newValue.weightList.length === 9) {
                // 兼容旧格式
                weightList.value = newValue.weightList;
            }
            if (newValue?.normList && newValue.normList.length === 9) {
                // 兼容旧格式
                normList.value = newValue.normList;
            }
        } finally {
            // 使用 nextTick 确保在下一个事件循环中重置标志
            nextTick(() => {
                isUpdatingFromParent.value = false;
            });
        }
    },
    { immediate: true }
);
</script>

<style lang="less" scoped>
.team-role-config {
    margin: 20px 0;
}

.sub-title {
    margin: 20px 0 10px 0;

    h4 {
        font-size: 14px;
        font-weight: 600;
        color: #1d2129;
        margin: 0;
    }
}

.section-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 15px 0 10px 0;

    h5 {
        font-size: 13px;
        font-weight: 500;
        color: #1d2129;
        margin: 0;
    }
}

.error-tip {
    color: #f53f3f;
    font-size: 12px;
    margin-top: 5px;
}

.reference {
    gap: var(--size-3);
    .w-full;
    .group;
}

.center {
    display: flex;
    justify-content: center;
    align-items: center;
}
</style>
